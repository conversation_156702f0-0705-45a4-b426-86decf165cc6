#!/usr/bin/env python3
"""
OMOP Vocabulary Loader Script

This script loads OMOP vocabularies from Athena CSV files into PostgreSQL.
It follows the official OHDSI loading sequence and provides detailed progress tracking.

Loading Order Based On:
- OHDSI/ETL-Synthea: https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r
- sidataplus community: https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py
- OHDSI Forums: https://forums.ohdsi.org/t/what-is-the-recommended-way-to-load-vocabulary-into-postgres/16467

Technical Approach:
- Uses PostgreSQL COPY command for maximum performance
- Loads CONCEPT.csv first (contains all vocabulary metadata)
- Handles tab-delimited format with proper quote escaping

Usage:
    python scripts/load_vocabularies.py

Requirements:
    - Athena vocabulary files in data/vocabulary/omop_v5_* directory
    - PostgreSQL OMOP database configured in .env
    - psycopg2-binary package installed

Configuration:
    - Set VOCABULARY_PATH in .env to specify exact directory, or
    - <PERSON><PERSON><PERSON> will auto-detect most recent omop_v5_* directory in data/vocabulary/
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
import psycopg2
from psycopg2 import sql

# Load environment variables
load_dotenv()

# Database configuration
# Note: Using superuser for constraint management (required for session_replication_role)
DB_CONFIG = {
    'host': os.getenv('OMOP_DB_HOST'),
    'port': os.getenv('OMOP_DB_PORT'),
    'database': os.getenv('OMOP_DB_NAME'),
    'user': os.getenv('OMOP_DB_SUPERUSER', os.getenv('USER')),  # Use system user as superuser
    'password': os.getenv('OMOP_DB_SUPERUSER_PASSWORD', '')     # No password for local superuser
}

# Vocabulary files in official OHDSI loading order
# Sources:
# - OHDSI/ETL-Synthea: https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r
# - sidataplus community: https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py
VOCABULARY_FILES = [
    'CONCEPT.csv',           # 1. Central metadata table (must be first)
    'VOCABULARY.csv',        # 2. References concept
    'CONCEPT_ANCESTOR.csv',  # 3. Hierarchical relationships
    'CONCEPT_RELATIONSHIP.csv', # 4. Concept relationships
    'RELATIONSHIP.csv',      # 5. Relationship types
    'CONCEPT_SYNONYM.csv',   # 6. Alternative names
    'DOMAIN.csv',           # 7. Domain definitions
    'CONCEPT_CLASS.csv',    # 8. Concept classes
    'DRUG_STRENGTH.csv'     # 9. Drug strength data
]

def get_vocabulary_path():
    """Get vocabulary directory path from environment or auto-detect."""

    # First, check if explicitly set in environment
    env_path = os.getenv('VOCABULARY_PATH')
    if env_path:
        vocab_path = Path(env_path)
        if vocab_path.exists():
            return vocab_path
        else:
            raise FileNotFoundError(f"Environment VOCABULARY_PATH not found: {vocab_path}")

    # Auto-detect: find most recent omop_v5_* directory
    vocab_base_dir = Path('./data/vocabulary')
    if not vocab_base_dir.exists():
        raise FileNotFoundError(f"Vocabulary base directory not found: {vocab_base_dir}")

    # Find all directories matching omop_v5_* pattern
    omop_dirs = list(vocab_base_dir.glob('omop_v5_*'))

    if not omop_dirs:
        raise FileNotFoundError(f"No omop_v5_* directories found in {vocab_base_dir}")

    # Sort by name (which includes date) and take the most recent
    most_recent = sorted(omop_dirs)[-1]

    print(f"📁 Auto-detected vocabulary directory: {most_recent}")
    return most_recent

def count_csv_rows(file_path):
    """Count rows in CSV file (excluding header)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f) - 1  # Exclude header
    except Exception:
        return 0

def load_csv_to_table(cursor, csv_file, table_name, vocab_path):
    """Load CSV file into PostgreSQL table using COPY command."""
    
    file_path = vocab_path / csv_file
    if not file_path.exists():
        print(f"⚠️  File not found: {csv_file} - Skipping")
        return 0
    
    # Count rows for progress tracking
    total_rows = count_csv_rows(file_path)
    print(f"📁 Loading {csv_file} → {table_name} ({total_rows:,} rows)")
    
    start_time = time.time()
    
    try:
        # Use PostgreSQL COPY command for fast bulk loading
        with open(file_path, 'r', encoding='utf-8') as f:
            # Skip header line
            next(f)
            
            # Use COPY command for efficient loading
            cursor.copy_expert(
                sql.SQL("COPY {} FROM STDIN WITH CSV DELIMITER E'\\t' QUOTE E'\\b'").format(
                    sql.Identifier(table_name.lower())
                ),
                f
            )
        
        elapsed = time.time() - start_time
        rate = total_rows / elapsed if elapsed > 0 else 0
        print(f"✅ Loaded {total_rows:,} rows in {elapsed:.1f}s ({rate:,.0f} rows/sec)")
        return total_rows
        
    except Exception as e:
        print(f"❌ Error loading {csv_file}: {e}")
        return 0

def main():
    """Main vocabulary loading process."""
    
    print("🚀 OMOP Vocabulary Loader Starting...")
    print("=" * 60)
    
    # Verify vocabulary directory
    try:
        vocab_path = get_vocabulary_path()
        print(f"📂 Vocabulary directory: {vocab_path}")
    except FileNotFoundError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    # Connect to database
    try:
        print(f"🔌 Connecting to database: {DB_CONFIG['database']}")
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = True  # Auto-commit for COPY commands
        cursor = conn.cursor()
        print("✅ Database connection established")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        sys.exit(1)

    # Disable foreign key constraints temporarily
    # Source: https://forums.ohdsi.org/t/what-is-the-recommended-way-to-load-vocabulary-into-postgres/16467
    try:
        print("🔓 Disabling foreign key constraints...")
        cursor.execute("SET session_replication_role = replica;")
        print("✅ Foreign key constraints disabled")
    except Exception as e:
        print(f"❌ Failed to disable constraints: {e}")
        print("⚠️  Note: This requires superuser privileges")
        sys.exit(1)

    print("=" * 60)
    
    # Load vocabulary files in order
    total_loaded = 0
    start_time = time.time()
    
    for csv_file in VOCABULARY_FILES:
        table_name = csv_file.replace('.csv', '').lower()
        rows_loaded = load_csv_to_table(cursor, csv_file, table_name, vocab_path)
        total_loaded += rows_loaded
        print()  # Empty line for readability
    
    # Final summary
    total_time = time.time() - start_time
    print("=" * 60)
    print(f"🎉 Vocabulary loading completed!")
    print(f"📊 Total records loaded: {total_loaded:,}")
    print(f"⏱️  Total time: {total_time:.1f} seconds")
    print(f"🚀 Average rate: {total_loaded/total_time:,.0f} records/sec")
    
    # Verify key tables
    print("\n🔍 Verifying vocabulary tables:")
    for csv_file in VOCABULARY_FILES[:4]:  # Check first 4 tables
        table_name = csv_file.replace('.csv', '').lower()
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"✅ {table_name}: {count:,} records")
        except Exception as e:
            print(f"❌ {table_name}: Error - {e}")

    # Re-enable foreign key constraints
    try:
        print("\n🔒 Re-enabling foreign key constraints...")
        cursor.execute("SET session_replication_role = DEFAULT;")
        print("✅ Foreign key constraints re-enabled")
    except Exception as e:
        print(f"⚠️  Warning: Failed to re-enable constraints: {e}")

    cursor.close()
    conn.close()
    print("\n✅ Vocabulary loading process completed successfully!")

if __name__ == "__main__":
    main()
