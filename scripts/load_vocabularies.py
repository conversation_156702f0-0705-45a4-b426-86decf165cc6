#!/usr/bin/env python3
"""
OMOP Vocabulary Loader Script

This script loads OMOP vocabularies from Athena CSV files into PostgreSQL.
It follows the official OHDSI loading sequence and provides detailed progress tracking.

Usage:
    python scripts/load_vocabularies.py

Requirements:
    - Athena vocabulary files in data/vocabulary/athena_download/
    - PostgreSQL OMOP database configured in .env
    - psycopg2-binary package installed
"""

import os
import sys
import time
import csv
from pathlib import Path
from dotenv import load_dotenv
import psycopg2
from psycopg2 import sql

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('OMOP_DB_HOST'),
    'port': os.getenv('OMOP_DB_PORT'),
    'database': os.getenv('OMOP_DB_NAME'),
    'user': os.getenv('OMOP_DB_USERNAME'),
    'password': os.getenv('OMOP_DB_PASSWORD')
}

# Vocabulary files in loading order (dependencies matter!)
VOCABULARY_FILES = [
    'VOCABULARY.csv',
    'DOMAIN.csv', 
    'CONCEPT_CLASS.csv',
    'CONCEPT.csv',
    'RELATIONSHIP.csv',
    'CONCEPT_RELATIONSHIP.csv',
    'CONCEPT_SYNONYM.csv',
    'CONCEPT_ANCESTOR.csv',
    'DRUG_STRENGTH.csv'
]

def get_vocabulary_path():
    """Get vocabulary directory path from environment."""
    vocab_path = Path(os.getenv('VOCABULARY_PATH', './data/vocabulary/athena_download'))
    if not vocab_path.exists():
        raise FileNotFoundError(f"Vocabulary directory not found: {vocab_path}")
    return vocab_path

def count_csv_rows(file_path):
    """Count rows in CSV file (excluding header)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for line in f) - 1  # Exclude header
    except Exception:
        return 0

def load_csv_to_table(cursor, csv_file, table_name, vocab_path):
    """Load CSV file into PostgreSQL table using COPY command."""
    
    file_path = vocab_path / csv_file
    if not file_path.exists():
        print(f"⚠️  File not found: {csv_file} - Skipping")
        return 0
    
    # Count rows for progress tracking
    total_rows = count_csv_rows(file_path)
    print(f"📁 Loading {csv_file} → {table_name} ({total_rows:,} rows)")
    
    start_time = time.time()
    
    try:
        # Use PostgreSQL COPY command for fast bulk loading
        with open(file_path, 'r', encoding='utf-8') as f:
            # Skip header line
            next(f)
            
            # Use COPY command for efficient loading
            cursor.copy_expert(
                sql.SQL("COPY {} FROM STDIN WITH CSV DELIMITER E'\\t' QUOTE E'\\b'").format(
                    sql.Identifier(table_name.lower())
                ),
                f
            )
        
        elapsed = time.time() - start_time
        rate = total_rows / elapsed if elapsed > 0 else 0
        print(f"✅ Loaded {total_rows:,} rows in {elapsed:.1f}s ({rate:,.0f} rows/sec)")
        return total_rows
        
    except Exception as e:
        print(f"❌ Error loading {csv_file}: {e}")
        return 0

def main():
    """Main vocabulary loading process."""
    
    print("🚀 OMOP Vocabulary Loader Starting...")
    print("=" * 60)
    
    # Verify vocabulary directory
    try:
        vocab_path = get_vocabulary_path()
        print(f"📂 Vocabulary directory: {vocab_path}")
    except FileNotFoundError as e:
        print(f"❌ {e}")
        sys.exit(1)
    
    # Connect to database
    try:
        print(f"🔌 Connecting to database: {DB_CONFIG['database']}")
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = True  # Auto-commit for COPY commands
        cursor = conn.cursor()
        print("✅ Database connection established")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        sys.exit(1)
    
    print("=" * 60)
    
    # Load vocabulary files in order
    total_loaded = 0
    start_time = time.time()
    
    for csv_file in VOCABULARY_FILES:
        table_name = csv_file.replace('.csv', '').lower()
        rows_loaded = load_csv_to_table(cursor, csv_file, table_name, vocab_path)
        total_loaded += rows_loaded
        print()  # Empty line for readability
    
    # Final summary
    total_time = time.time() - start_time
    print("=" * 60)
    print(f"🎉 Vocabulary loading completed!")
    print(f"📊 Total records loaded: {total_loaded:,}")
    print(f"⏱️  Total time: {total_time:.1f} seconds")
    print(f"🚀 Average rate: {total_loaded/total_time:,.0f} records/sec")
    
    # Verify key tables
    print("\n🔍 Verifying vocabulary tables:")
    for csv_file in VOCABULARY_FILES[:4]:  # Check first 4 tables
        table_name = csv_file.replace('.csv', '').lower()
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"✅ {table_name}: {count:,} records")
        except Exception as e:
            print(f"❌ {table_name}: Error - {e}")
    
    cursor.close()
    conn.close()
    print("\n✅ Vocabulary loading process completed successfully!")

if __name__ == "__main__":
    main()
