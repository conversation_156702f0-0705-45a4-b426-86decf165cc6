# Development Documentation

This section contains documentation related to the development environment, processes, and standards for the FHIR to OMOP transformation project.

## Environment Setup

- [Environment Setup](environment_setup.md) - Setting up the development environment

## Development Standards

- [Coding and Documentation Standards](standards.md) - Comprehensive guidelines for code style, documentation format, and development practices

## Directory Structure

```
development/
├── environment_setup.md           # Development environment setup guide
├── shell-path-troubleshooting.md  # Shell and PATH troubleshooting guide
├── standards.md                   # Coding and documentation standards
```

## Related Documentation

- [Contributing Guidelines](../../../CONTRIBUTING.md) - General guidelines for contributing to the project

## Future Documentation

As the project progresses, additional documentation will be added for:

- Testing strategies and frameworks
- Continuous integration and deployment
- Code review processes
