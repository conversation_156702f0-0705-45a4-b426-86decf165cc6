# OMOP CDM Database Setup Guide

This guide provides an overview of database options for the OMOP Common Data Model (CDM) v5.4 to use with the FHIR to OMOP transformation project. For detailed, step-by-step instructions, please refer to our specific setup guides for each database system.

## Official Resources

- [OHDSI CommonDataModel Repository](https://github.com/OHDSI/CommonDataModel)
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
- [DDL Files for CDM v5.4](https://github.com/OHDSI/CommonDataModel/tree/v5.4.0/inst/ddl/5.4)

## Database Options

You have two main options for the OMOP CDM database:

1. **PostgreSQL** (recommended for production)
   - Officially supported by OHDSI
   - Better performance for large datasets
   - Scalable for production environments
   - [Detailed PostgreSQL Setup Guide](postgresql_setup.md)

2. **SQLite** (simpler for development/testing)
   - Lightweight, file-based database
   - No server installation required
   - Ideal for development and testing
   - Limited scalability and performance
   - [Detailed SQLite Setup Guide](sqlite_setup.md)

## Choosing the Right Database

### When to Use PostgreSQL

- For production environments
- When working with large datasets
- When multiple users need concurrent access
- When you need advanced database features
- When you plan to use the full OHDSI tool suite

### When to Use SQLite

- For development and testing
- When working with small datasets
- For single-user environments
- When you need a portable solution
- When you want to avoid complex server setup

## Database Setup Process Overview

Regardless of which database system you choose, the general process for setting up an OMOP CDM database includes:

1. **Create the database and user**
2. **Create the OMOP CDM tables**
3. **Add primary keys, indices, and constraints**
4. **Load vocabulary data**
5. **Verify the installation**

## Detailed Setup Guides

For step-by-step instructions, please refer to our detailed setup guides:

- [PostgreSQL Setup Guide](postgresql_setup.md) - Recommended for production
- [SQLite Setup Guide](sqlite_setup.md) - Recommended for development

## Alternative: Using the OHDSI R Package

OHDSI provides an R package that can generate and execute DDL scripts for various database systems. This is particularly useful for PostgreSQL and other officially supported database systems:

```R
# Install required packages
install.packages("devtools")
devtools::install_github("OHDSI/CommonDataModel")
devtools::install_github("ohdsi/DatabaseConnector")

# Generate DDL files
CommonDataModel::buildRelease(cdmVersions = "5.4",
                             targetDialects = "postgresql",
                             outputfolder = "/path/to/output")

# Or execute DDL directly
cd <- DatabaseConnector::createConnectionDetails(dbms = "postgresql",
                                               server = "localhost/omop_cdm",
                                               user = "omop",
                                               password = "your_secure_password",
                                               pathToDriver = "/path/to/jdbc/driver")

CommonDataModel::executeDdl(connectionDetails = cd,
                          cdmVersion = "5.4",
                          cdmDatabaseSchema = "public")
```

## Vocabulary Setup

After setting up the database structure, you need to load the OMOP vocabularies. This process is described in detail in our database-specific setup guides.

The general process includes:

1. **Download vocabularies from [Athena](https://athena.ohdsi.org/)**
   - Register for an account
   - Select the vocabularies you need (SNOMED, LOINC, RxNorm, etc.)
   - Download the vocabulary files

2. **Load vocabularies into your database**
   - For PostgreSQL: Use the `\COPY` command
   - For SQLite: Use a Python script or SQLite's `.import` command

## Next Steps

After setting up the database:

1. Configure the environment variables in `.env`
2. Run the ETL process to transform FHIR data to OMOP
3. Consider running data quality checks using the [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard)

## References

1. [OHDSI Common Data Model](https://ohdsi.github.io/CommonDataModel/)
2. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
3. [OHDSI GitHub Repository](https://github.com/OHDSI/CommonDataModel)
