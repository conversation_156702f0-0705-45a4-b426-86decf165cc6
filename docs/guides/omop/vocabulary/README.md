# OMOP Vocabulary Management Guide

This comprehensive guide covers everything you need to know about OMOP vocabularies, from understanding their purpose to loading them into your database.

## 🔗 Prerequisites

Before working with vocabularies, ensure you have completed the database setup:

> **📋 Required**: [PostgreSQL OMOP Database Setup](../database/postgresql_setup.md) must be completed first.

Your database should have:
- ✅ 39 OMOP CDM tables created
- ✅ User `omop` with proper permissions  
- ✅ Environment variables configured
- ✅ Connection verified

## 📚 Documentation Structure

This vocabulary guide is organized into focused sections:

### 1. [Overview](overview.md) - Understanding OMOP Vocabularies
- **What are vocabularies** and why they're critical
- **Vocabulary tables** and their relationships
- **Standard vs source concepts** explained
- **Domains and concept classes** overview

### 2. [Athena Setup](athena_setup.md) - Downloading Vocabularies  
- **Account registration** on OHDSI Athena
- **Vocabulary selection** for your use case
- **Download process** and file formats
- **Licensing considerations** for different vocabularies

### 3. [Loading Guide](loading.md) - Database Integration
- **Automated loading** with Python scripts
- **Manual loading** with PostgreSQL commands  
- **Verification procedures** to ensure success
- **Troubleshooting** common issues

## 🚀 Quick Start Workflow

For users who have completed the PostgreSQL setup:

### Step 1: Download Vocabularies
```bash
# Register at https://athena.ohdsi.org/
# Select minimum vocabularies: SNOMED, LOINC, RxNorm, Gender, Race
# Download and extract to: data/vocabulary/athena_download/
```

### Step 2: Load Vocabularies  
```bash
# Using automated script (recommended)
python scripts/load_vocabularies.py

# Expected result: ~6M+ concepts loaded
```

### Step 3: Verify Installation
```bash
# Check vocabulary counts
PGPASSWORD='omop_secure_2024' /opt/homebrew/opt/postgresql@14/bin/psql -U omop -d omop_cdm -c "SELECT vocabulary_id, COUNT(*) FROM concept GROUP BY vocabulary_id ORDER BY COUNT(*) DESC;"
```

## 📊 Expected Results

After successful vocabulary loading, you should have:

| Vocabulary | Approximate Concepts | Purpose |
|------------|---------------------|---------|
| **SNOMED** | ~400,000 | Clinical conditions, procedures |
| **LOINC** | ~100,000 | Laboratory tests, measurements |
| **RxNorm** | ~200,000 | Medications and drugs |
| **Gender** | ~10 | Patient gender values |
| **Race** | ~50 | Patient race/ethnicity values |

## 🔄 Integration with FHIR-to-OMOP

Once vocabularies are loaded, your database is ready for:

- **FHIR resource mapping** using standard concepts
- **ETL processes** that transform source codes
- **Analytics queries** across standardized data
- **Interoperability** with other OMOP databases

## 📖 Additional Resources

- **[OHDSI Athena](https://athena.ohdsi.org/)** - Official vocabulary portal
- **[OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)** - Complete specification
- **[Vocabulary Tables Overview PDF](OMOP-Vocabulary-Tables-Overview_lowres_2022-11-02.pdf)** - Visual reference

## 🆘 Support

If you encounter issues:

1. **Check Prerequisites**: Ensure PostgreSQL setup is complete
2. **Review Logs**: Check script output for specific errors  
3. **Verify Files**: Ensure Athena files are properly downloaded
4. **Environment**: Confirm `.env` configuration is correct

---

> **Next Steps**: After vocabulary loading, proceed to [FHIR-to-OMOP Mapping](../mapping/README.md) to start transforming healthcare data.
