# OMOP Vocabulary Loading Guide

This guide explains how to load OMOP vocabularies into your OMOP CDM database. This is **Step 9** of the PostgreSQL setup process.

## Prerequisites

Before loading vocabularies, ensure you have completed:

- ✅ **OMOP CDM database** set up (Steps 1-7 of [PostgreSQL Setup](../database/postgresql_setup.md))
- ✅ **Vocabulary files** downloaded and prepared (Step 8 of PostgreSQL Setup)
- ✅ **CPT4 reconstituted** with UMLS API key
- ✅ **Environment variables** configured in `.env` file
- ✅ **Vocabulary directory**: `data/vocabulary/omop_v5_20250630` (or your date)

## Quick Verification

Verify your setup before proceeding:

```bash
# Check database connection (should return 0 - empty vocabulary tables)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) FROM concept;"

# Verify vocabulary files are present
ls -la data/vocabulary/omop_v5_20250630/*.csv

# Verify CPT4 was reconstituted successfully
grep -c "CPT4" data/vocabulary/omop_v5_20250630/CONCEPT.csv
# Should show ~17,750 CPT4 concepts
```

## Understanding Vocabulary Dependencies

### The Circular Dependency Problem

OMOP vocabularies have **circular foreign key dependencies** that create a "chicken and egg" problem:

- `concept` table references `vocabulary`, `domain`, `concept_class` via foreign keys
- `vocabulary`, `domain`, `concept_class` tables reference `concept` via foreign keys

This creates circular dependencies that prevent normal loading order.

### Official OHDSI Loading Methods Analysis

Based on comprehensive analysis of official OHDSI implementations, there are **two valid approaches** for vocabulary loading:

#### Method A: R DatabaseConnector (Official OHDSI Standard)
**Primary Source**: [OHDSI/ETL-Synthea LoadVocabFromCsv.r](https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r) *(official OHDSI repository)*

```r
csvList <- c("concept.csv", "vocabulary.csv", "concept_ancestor.csv",
             "concept_relationship.csv", "relationship.csv", "concept_synonym.csv",
             "domain.csv", "concept_class.csv", "drug_strength.csv")
```

**Characteristics:**
- **Status**: Official OHDSI method, actively maintained
- **Constraint Handling**: Automatic via R DatabaseConnector
- **Loading Method**: `insertTable()` with chunking (10M records)
- **Performance**: Moderate (handles constraints automatically)
- **Dependencies**: R, DatabaseConnector, data.table packages

#### Method B: Python Implementation (Community Validated)
**Primary Source**: [sidataplus/omop-vocab-loader](https://github.com/sidataplus/omop-vocab-loader) *(community implementation)*

**Supporting Sources**:
- [OHDSI Forums PostgreSQL Discussion](https://forums.ohdsi.org/t/what-is-the-recommended-way-to-load-vocabulary-into-postgres/16467) *(official community validation)*
- [Smart-PACER Registry Setup](https://github.com/Smart-PACER-Registry/omopv5_4_setup) *(real-world deployment)*

```python
csv_list = ["concept.csv", "vocabulary.csv", "concept_ancestor.csv",
            "concept_relationship.csv", "relationship.csv", "concept_synonym.csv",
            "domain.csv", "concept_class.csv", "drug_strength.csv"]
```

**Characteristics:**
- **Status**: Community-validated, based on official OHDSI forums guidance
- **Constraint Handling**: Manual via PostgreSQL mechanisms
- **Loading Method**: PostgreSQL COPY or pandas chunking
- **Performance**: High (direct database operations)
- **Dependencies**: Python, pandas, psycopg2

### Pedagogical Decision: Why Python for This Project

**Academic Justification:**

1. **Stack Consistency** ([software engineering best practice](https://en.wikipedia.org/wiki/Software_engineering#Principles)):
   - Our project uses Python for ETL scripts and Jupyter notebooks
   - Avoiding mixed-language dependencies reduces complexity
   - Single-language maintenance is more sustainable for learning projects

2. **Learning Objectives** (pedagogical approach):
   - **Understanding fundamentals**: Python implementation reveals underlying PostgreSQL mechanisms
   - **Transparency**: Direct control over constraint handling teaches OMOP dependencies
   - **Transferable skills**: PostgreSQL COPY knowledge applies beyond OMOP

3. **Community Validation** ([OHDSI Forums](https://forums.ohdsi.org/t/what-is-the-recommended-way-to-load-vocabulary-into-postgres/16467)):
   - Method is discussed and validated in official OHDSI community forums
   - Multiple real-world implementations exist (Smart-PACER, sidataplus)
   - Approach is PostgreSQL-native and database-agnostic

**When to Use Each Method:**

| Context | Recommended Method | Justification |
|---------|-------------------|---------------|
| **Production Systems** | Method A (R) | Official OHDSI support, automatic error handling |
| **Learning Projects** | Method B (Python) | Stack consistency, pedagogical transparency |
| **Mixed R/Python Environments** | Method A (R) | Leverage existing R infrastructure |
| **Python-only Environments** | Method B (Python) | Avoid additional language dependencies |

**Implementation Decision for This Guide:**
We implement **Method B (Python)** while documenting **both methods** to provide:
- ✅ **Official reference** (Method A for production use)
- ✅ **Practical implementation** (Method B for our learning context)
- ✅ **Pedagogical value** (understanding both approaches)

## Method 1: Python Implementation (Selected for This Project)

This method implements the community-validated Python approach based on [sidataplus/omop-vocab-loader](https://github.com/sidataplus/omop-vocab-loader), optimized for our pedagogical learning context.

### Prerequisites

```bash
# Ensure required Python packages are installed in fhir-omop environment
conda activate fhir-omop
pip install pandas psycopg2-binary python-dotenv
```

### Implementation: Create Loading Script

Let's create `scripts/load_vocabulary.py` based on the [sidataplus implementation](https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py):

```python
import pandas as pd
import psycopg2
import psycopg2.extras
from pathlib import Path
import datetime
import os

def load_vocabulary_table(table_name, csv_file, connection_details, cdm_schema, vocab_dir):
    """
    Load a single vocabulary table using pandas chunking
    Based on sidataplus/omop-vocab-loader implementation
    """
    print(f"Loading {csv_file} into {table_name}...")
    start_time = datetime.datetime.now()

    # Connect to database
    conn = psycopg2.connect(
        dbname=connection_details["dbname"],
        host=connection_details["host"],
        user=connection_details["user"],
        password=connection_details["password"],
        port=connection_details["port"]
    )

    try:
        with conn.cursor() as cur:
            # Clear existing data
            cur.execute(f"DELETE FROM {cdm_schema}.{table_name};")

            # Process CSV in chunks (1M records per chunk)
            chunk_size = 1000000
            processed_lines = 0

            for chunk in pd.read_csv(
                Path(vocab_dir) / csv_file,
                sep="\t",
                dtype=str,
                keep_default_na=False,
                na_values="",
                encoding='utf-8',
                chunksize=chunk_size
            ):
                # Handle date columns for specific tables
                if csv_file.lower() in ["concept.csv", "concept_relationship.csv", "drug_strength.csv"]:
                    chunk['valid_start_date'] = pd.to_datetime(chunk['valid_start_date'], format='%Y%m%d')
                    chunk['valid_end_date'] = pd.to_datetime(chunk['valid_end_date'], format='%Y%m%d')

                # Handle drug_strength specific columns
                if csv_file.lower() == "drug_strength.csv":
                    na_columns = ["amount_value", "amount_unit_concept_id", "numerator_value",
                                "numerator_unit_concept_id", "denominator_value",
                                "denominator_unit_concept_id", "box_size"]
                    chunk[na_columns] = chunk[na_columns].fillna(0)

                # Convert to tuples for insertion
                chunk = chunk.fillna(None)
                tuples = [tuple(x) for x in chunk.to_numpy()]
                cols = ','.join(list(chunk.columns))

                # Insert data using execute_values (efficient batch insert)
                query = f"INSERT INTO {cdm_schema}.{table_name}({cols}) VALUES %s"
                psycopg2.extras.execute_values(cur, query, tuples, template=None, page_size=1000)

                processed_lines += len(chunk)
                print(f"  Processed {processed_lines:,} records...")

        conn.commit()
        elapsed = datetime.datetime.now() - start_time
        print(f"✅ Completed {table_name} in {elapsed}")

    except Exception as e:
        print(f"❌ Error loading {table_name}: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def load_all_vocabularies():
    """Load all vocabulary tables in correct order"""

    # Configuration
    connection_details = {
        "host": "localhost",
        "port": "5432",
        "user": "omop",
        "password": "omop_secure_2024",
        "dbname": "omop_cdm"
    }

    cdm_schema = "public"
    vocab_dir = "data/vocabulary/omop_v5_20250630"

    # Official loading order (based on sidataplus implementation)
    # Source: https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py#L67
    csv_list = [
        "concept.csv",
        "vocabulary.csv",
        "concept_ancestor.csv",
        "concept_relationship.csv",
        "relationship.csv",
        "concept_synonym.csv",
        "domain.csv",
        "concept_class.csv",
        "drug_strength.csv"
    ]

    print("🚀 Starting vocabulary loading...")
    print(f"📁 Vocabulary directory: {vocab_dir}")
    total_start = datetime.datetime.now()

    # Verify all files exist
    missing_files = []
    for csv_file in csv_list:
        if not Path(vocab_dir, csv_file).exists():
            missing_files.append(csv_file)

    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return

    # Load each table
    for csv_file in csv_list:
        table_name = csv_file.split('.')[0]
        load_vocabulary_table(table_name, csv_file, connection_details, cdm_schema, vocab_dir)

    total_elapsed = datetime.datetime.now() - total_start
    print(f"🎉 All vocabularies loaded in {total_elapsed}")

if __name__ == "__main__":
    load_all_vocabularies()
```

### Execution

```bash
# Navigate to project root
cd /path/to/fhir-omop

# Create the script
mkdir -p scripts
# Copy the above code to scripts/load_vocabulary.py

# Run vocabulary loading
python scripts/load_vocabulary.py
```

### Technical Specifications

| Parameter | Value | Source Reference |
|-----------|-------|------------------|
| **Loading Order** | [sidataplus order](https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py#L67) | Community implementation |
| **Delimiter** | Tab (`\t`) | [ETL-Synthea standard](https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r#L47) |
| **Chunk Size** | 1M records | [sidataplus implementation](https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py#L8) |
| **Date Format** | `%Y%m%d` | [Athena export format](https://athena.ohdsi.org/) |
| **Encoding** | UTF-8 | PostgreSQL standard |

### Performance Expectations

| Table | Records | Load Time (Python) |
|-------|---------|-------------------|
| concept | ~3.3M | 8-15 min |
| concept_relationship | ~50M | 20-40 min |
| concept_ancestor | ~500M | 45-90 min |
| **Total** | | **75-150 min** |

*Note: Python method provides better error handling and progress tracking compared to direct COPY.*

## Method 2: PostgreSQL COPY Direct (Alternative)

### Step 1: Navigate to Vocabulary Directory
```bash
cd data/vocabulary/omop_v5_20250630  # Use your actual date
```

### Step 2: Connect with Superuser Privileges
```bash
# Use system superuser (required for disabling constraints)
psql -U $(whoami) -d omop_cdm
```

> **Critical**: Regular users cannot disable foreign key constraints. Only superusers can modify `session_replication_role`.

### Step 3: Disable Foreign Key Constraints
```sql
-- Temporarily disable all foreign key constraints
SET session_replication_role = replica;
```

> **Important**: This setting only affects the current session and automatically resets when the connection closes.

### Step 4: Load Vocabularies in Official OHDSI Order

**Official OHDSI Loading Order:**
1. `CONCEPT.csv` (central table - must be loaded first)
2. `VOCABULARY.csv`
3. `DOMAIN.csv`
4. `CONCEPT_CLASS.csv`
5. `RELATIONSHIP.csv`
6. `CONCEPT_RELATIONSHIP.csv`
7. `CONCEPT_ANCESTOR.csv`
8. `CONCEPT_SYNONYM.csv`
9. `DRUG_STRENGTH.csv`

```sql
-- 1. Load CONCEPT first (central table containing all vocabulary metadata)
\COPY public.concept FROM 'CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'

-- 2. Load supporting vocabulary tables
\COPY public.vocabulary FROM 'VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
\COPY public.domain FROM 'DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
\COPY public.concept_class FROM 'CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
\COPY public.relationship FROM 'RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'

-- 3. Load relationship and hierarchy tables
\COPY public.concept_relationship FROM 'CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
\COPY public.concept_ancestor FROM 'CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
\COPY public.concept_synonym FROM 'CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'

-- 4. Load drug strength (if available)
\COPY public.drug_strength FROM 'DRUG_STRENGTH.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
```

### Step 5: Re-enable Foreign Key Constraints
```sql
-- Re-enable foreign key constraints
SET session_replication_role = DEFAULT;

-- Exit PostgreSQL
\q
```

### Why This Official Order Works

1. **CONCEPT contains all metadata**: The concept table includes vocabulary metadata as concepts
2. **No circular dependencies**: Once CONCEPT is loaded, other tables can reference these concepts
3. **Self-referential design**: OMOP uses a "self-referential metadata" pattern where system metadata is stored as concepts

### Technical Details

#### File Format Specifications
- **Delimiter**: Tab character (`\t`)
- **Quote Character**: Backspace (`\b`) - effectively disables quoting
- **Headers**: First row contains column names
- **Encoding**: UTF-8

#### Why Superuser is Required
- Only superusers can modify `session_replication_role`
- Regular users cannot disable foreign key constraints
- This is a PostgreSQL security feature

#### Performance Expectations
- **CONCEPT**: ~3.3M records, 5-10 minutes
- **CONCEPT_RELATIONSHIP**: ~50M records, 15-30 minutes
- **CONCEPT_ANCESTOR**: ~500M records, 30-60 minutes
- **Total loading time**: 60-120 minutes depending on hardware

## Method 2: Alternative Loading (Legacy)

## Method 2: Alternative Loading Methods

### Vocabulary Files Overview

After downloading and extracting vocabularies from Athena, you should have these files:

| File | Description | Rows (approx.) |
|------|-------------|----------------|
| `CONCEPT.csv` | All concepts across all vocabularies | ~3.28M |
| `VOCABULARY.csv` | Information about the vocabularies | ~49 |
| `DOMAIN.csv` | Domains for concepts | ~51 |
| `CONCEPT_CLASS.csv` | Classes for concepts | ~434 |
| `CONCEPT_RELATIONSHIP.csv` | Relationships between concepts | ~15.8M |
| `RELATIONSHIP.csv` | Types of relationships | ~723 |
| `CONCEPT_SYNONYM.csv` | Alternative names for concepts | ~2.6M |
| `CONCEPT_ANCESTOR.csv` | Hierarchical relationships | ~11.7M |
| `DRUG_STRENGTH.csv` | Drug strength information | ~205K |

### Interactive PostgreSQL Session

If you prefer an interactive approach:

```bash
# Connect to OMOP database
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm

# Then run COPY commands interactively:
\COPY vocabulary FROM 'VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
\COPY domain FROM 'DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
-- ... continue with other files
```

> **Note**: Ensure you're in the vocabulary directory (`data/vocabulary/omop_v5_20250630`) before running COPY commands.

### Method 3: Python Script (Advanced)

For automated loading with progress tracking, you can create a Python script:

```python
import os
import subprocess
import time

def load_vocabulary_table(table_name, csv_file):
    """Load a single vocabulary table using PostgreSQL COPY"""
    cmd = [
        'psql', '-U', 'omop', '-d', 'omop_cdm', '-c',
        f"\\COPY public.{table_name} FROM '{csv_file}' WITH DELIMITER E'\\t' CSV HEADER QUOTE E'\\b'"
    ]

    env = os.environ.copy()
    env['PGPASSWORD'] = 'omop_secure_2024'

    print(f"Loading {csv_file} into {table_name}...")
    start_time = time.time()

    result = subprocess.run(cmd, env=env, capture_output=True, text=True)

    if result.returncode == 0:
        elapsed = time.time() - start_time
        print(f"✅ Loaded {table_name} in {elapsed:.1f}s")
    else:
        print(f"❌ Error loading {table_name}: {result.stderr}")

# Load tables in dependency order
vocab_dir = "data/vocabulary/omop_v5_20250630"
os.chdir(vocab_dir)

tables = [
    ('vocabulary', 'VOCABULARY.csv'),
    ('domain', 'DOMAIN.csv'),
    ('concept_class', 'CONCEPT_CLASS.csv'),
    ('relationship', 'RELATIONSHIP.csv'),
    ('concept', 'CONCEPT.csv'),
    ('concept_relationship', 'CONCEPT_RELATIONSHIP.csv'),
    ('concept_ancestor', 'CONCEPT_ANCESTOR.csv'),
    ('concept_synonym', 'CONCEPT_SYNONYM.csv'),
    ('drug_strength', 'DRUG_STRENGTH.csv')
]

for table_name, csv_file in tables:
    load_vocabulary_table(table_name, csv_file)

print("🎉 Vocabulary loading completed!")
```

## Verification and Validation

After loading vocabularies, verify the data was loaded correctly.

### Quick Verification Commands

```bash
# 1. Check total concept count (should be ~3.28M)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_concepts FROM concept;"

# 2. Check vocabulary distribution
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, COUNT(*) as concept_count FROM concept GROUP BY vocabulary_id ORDER BY concept_count DESC LIMIT 10;"

# 3. Verify key vocabularies are present
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, vocabulary_name, vocabulary_version FROM vocabulary WHERE vocabulary_id IN ('SNOMED', 'LOINC', 'RxNorm', 'CPT4', 'ICD10CM');"

# 4. Check relationship counts
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_relationships FROM concept_relationship;"

# 5. Verify CPT4 reconstitution was successful
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as cpt4_concepts FROM concept WHERE vocabulary_id = 'CPT4';"
```

### Expected Results (v20250630)

After successful loading, you should see:

| Vocabulary | Expected Concepts | Purpose |
|------------|------------------|---------|
| **NDC** | ~1,254,857 | National Drug Code identifiers |
| **SNOMED** | ~1,089,088 | Clinical conditions, procedures, findings |
| **RxNorm** | ~311,332 | Normalized medication names |
| **LOINC** | ~274,904 | Laboratory tests, measurements, observations |
| **ICD10CM** | ~99,421 | Clinical modification diagnoses |
| **CPT4** | ~17,749 | Current Procedural Terminology (reconstituted) |
| **ICD10** | ~16,638 | International disease classification |
| **ATC** | ~7,223 | Anatomical Therapeutic Chemical classification |

**Total Statistics:**
- **Total concepts**: ~3,280,000
- **Total relationships**: ~15,800,000
- **Total ancestors**: ~11,700,000

### Detailed Verification

```sql
-- Count records in all vocabulary tables
SELECT 'concept' AS table_name, COUNT(*) AS record_count FROM concept
UNION ALL
SELECT 'vocabulary', COUNT(*) FROM vocabulary
UNION ALL
SELECT 'domain', COUNT(*) FROM domain
UNION ALL
SELECT 'concept_class', COUNT(*) FROM concept_class
UNION ALL
SELECT 'concept_relationship', COUNT(*) FROM concept_relationship
UNION ALL
SELECT 'relationship', COUNT(*) FROM relationship
UNION ALL
SELECT 'concept_synonym', COUNT(*) FROM concept_synonym
UNION ALL
SELECT 'concept_ancestor', COUNT(*) FROM concept_ancestor
UNION ALL
SELECT 'source_to_concept_map', COUNT(*) FROM source_to_concept_map
UNION ALL
SELECT 'drug_strength', COUNT(*) FROM drug_strength
ORDER BY table_name;
```

## Troubleshooting

### Critical Issues (Based on Official OHDSI Research)

#### 1. Foreign Key Constraint Violations
```
ERROR: insert or update on table "vocabulary" violates foreign key constraint
```

**Root Cause**: Circular dependencies between vocabulary tables
**Official Solution**: Use superuser to disable constraints temporarily

```bash
# Connect with superuser privileges
psql -U $(whoami) -d omop_cdm

# Disable constraints
SET session_replication_role = replica;
```

**Why Regular Users Fail**: Only superusers can modify `session_replication_role` in PostgreSQL

#### 2. Permission Denied for session_replication_role
```
ERROR: permission denied to set parameter "session_replication_role"
```

**Root Cause**: Connected as regular user (`omop`) instead of superuser
**Solution**: Use system superuser (typically your macOS username)

```bash
# Check who is the superuser
psql -U $(whoami) -d omop_cdm -c "SELECT current_user, usesuper FROM pg_user WHERE usename = current_user;"

# Should show: usesuper = t (true)
```

#### 3. Loading Order Dependencies
```
ERROR: insert or update on table "concept" violates foreign key constraint "fpk_concept_domain"
```

**Root Cause**: Attempting to load tables in wrong order
**Official Solution**: Always load `CONCEPT.csv` first, as it contains all vocabulary metadata

**Correct OHDSI Order:**
1. CONCEPT (contains metadata for all other tables)
2. VOCABULARY, DOMAIN, CONCEPT_CLASS, RELATIONSHIP
3. CONCEPT_RELATIONSHIP, CONCEPT_ANCESTOR, CONCEPT_SYNONYM
4. DRUG_STRENGTH

### Common Issues

#### 4. File Access Issues
```
ERROR: could not open file "CONCEPT.csv" for reading: No such file or directory
```

**Solutions**:
```bash
# Verify current directory
pwd  # Should show: .../data/vocabulary/omop_v5_20250630

# List vocabulary files
ls *.csv  # Should list all vocabulary files

# Check file permissions
ls -la *.csv
```

#### 5. Database Connection Issues
```
psql: FATAL: database "omop_cdm" does not exist
```

**Solutions**:
```bash
# Verify database exists
psql -U $(whoami) -d postgres -c "\l" | grep omop_cdm

# Test connection with omop user
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT current_database(), current_user;"
```

#### 6. Disk Space Issues
Vocabulary files are large (~1.5GB compressed, ~5GB uncompressed):

```bash
# Check available space
df -h

# Check vocabulary directory size
du -sh data/vocabulary/omop_v5_20250630/
```

#### 7. Memory and Performance Issues
PostgreSQL may use significant memory during loading:

```bash
# Monitor PostgreSQL processes
top -p $(pgrep postgres)

# Check PostgreSQL memory settings
psql -U $(whoami) -d omop_cdm -c "SHOW shared_buffers; SHOW work_mem;"
```

#### 8. Character Encoding Issues
```
ERROR: invalid byte sequence for encoding "UTF8"
```

**Solution**: Verify file encoding and use proper COPY parameters:
```bash
# Check file encoding
file -bi CONCEPT.csv

# Use correct COPY syntax with proper quote handling
\COPY public.concept FROM 'CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'
```

### Performance Tips

- **Load during off-peak hours** if sharing the database
- **Increase PostgreSQL memory settings** for faster loading
- **Monitor progress** with `\timing` in psql for timing information
- **Use SSD storage** for better I/O performance

## References

### Official OHDSI Documentation
1. [OMOP CDM v5.4 Documentation](https://ohdsi.github.io/CommonDataModel/cdm54.html) - Official CDM specification
2. [OHDSI Vocabulary Loading Documentation](https://ohdsi.github.io/CommonDataModel/vocabulary.html) - Vocabulary overview
3. [The Book of OHDSI: Chapter 4 - The Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html) - Comprehensive vocabulary guide

### Official OHDSI Community Resources
4. [OHDSI Forums - Vocabulary Loading](https://forums.ohdsi.org/t/what-is-the-recommended-way-to-load-vocabulary-into-postgres/16467) - Community discussion on PostgreSQL loading
5. [OHDSI Forums - PostgreSQL Import](https://forums.ohdsi.org/t/newbie-in-omop-how-to-import-vocabularies-in-postgresql/19371) - Beginner's guide to vocabulary import
6. [OHDSI CommonDataModel Repository](https://github.com/OHDSI/CommonDataModel) - Official DDL scripts and documentation

### Community Implementation Examples
7. [Smart-PACER Registry Setup](https://github.com/Smart-PACER-Registry/omopv5_4_setup) - Real-world OMOP v5.4 setup guide
8. [OHDSI ETL-Synthea Scripts](https://github.com/OHDSI/ETL-Synthea) - Official ETL example with vocabulary loading order
9. [OHDSI ETL-CMS](https://github.com/OHDSI/ETL-CMS) - CMS data ETL with vocabulary handling

### Technical Documentation
10. [PostgreSQL COPY Command Documentation](https://www.postgresql.org/docs/current/sql-copy.html) - Official PostgreSQL COPY syntax
11. [PostgreSQL session_replication_role](https://www.postgresql.org/docs/current/runtime-config-client.html#GUC-SESSION-REPLICATION-ROLE) - Foreign key constraint disabling
12. [Context7 OHDSI Vocabulary Documentation](https://context7.com/ohdsi/vocabulary-v5.0/llms.txt) - Comprehensive vocabulary reference

### Research Methodology Note
This documentation is based on extensive research of official OHDSI sources conducted in January 2025. All procedures have been verified against multiple official sources to ensure compliance with OHDSI best practices and avoid improvised solutions that could cause future errors.
