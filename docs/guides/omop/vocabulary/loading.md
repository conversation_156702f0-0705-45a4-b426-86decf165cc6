# OMOP Vocabulary Loading Guide

This guide explains how to load OMOP vocabularies into your OMOP CDM database. This is **Step 9** of the PostgreSQL setup process.

## Prerequisites

Before loading vocabularies, ensure you have completed:

- ✅ **OMOP CDM database** set up (Steps 1-7 of [PostgreSQL Setup](../database/postgresql_setup.md))
- ✅ **Vocabulary files** downloaded and prepared (Step 8 of PostgreSQL Setup)
- ✅ **CPT4 reconstituted** with UMLS API key
- ✅ **Environment variables** configured in `.env` file
- ✅ **Vocabulary directory**: `data/vocabulary/omop_v5_20250630` (or your date)

## Quick Verification

Verify your setup before proceeding:

```bash
# Check database connection (should return 0 - empty vocabulary tables)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) FROM concept;"

# Verify vocabulary files are present
ls -la data/vocabulary/omop_v5_20250630/*.csv

# Verify CPT4 was reconstituted successfully
grep -c "CPT4" data/vocabulary/omop_v5_20250630/CONCEPT.csv
# Should show ~17,750 CPT4 concepts
```

## Method 1: Manual Loading (Recommended)

### PostgreSQL COPY Commands

The most reliable method is using PostgreSQL's native COPY commands:

```bash
# Navigate to vocabulary directory
cd data/vocabulary/omop_v5_20250630  # Use your actual date

# 1. Load reference tables first (no dependencies)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.vocabulary FROM 'VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.domain FROM 'DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.concept_class FROM 'CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.relationship FROM 'RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

# 2. Load main concept table (depends on reference tables)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.concept FROM 'CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

# 3. Load relationship tables (depend on concept table)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.concept_relationship FROM 'CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.concept_ancestor FROM 'CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.concept_synonym FROM 'CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"

# 4. Load drug-specific table (depends on concept table)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\COPY public.drug_strength FROM 'DRUG_STRENGTH.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b'"
```

> **Performance Note**: Loading can take 30-60 minutes depending on hardware. The CONCEPT table (~3.3M rows) and CONCEPT_RELATIONSHIP table (~15.8M rows) take the longest.

### Why This Order Matters

The loading order respects foreign key dependencies:

1. **Reference tables** (vocabulary, domain, concept_class, relationship) have no dependencies
2. **Concept table** depends on reference tables
3. **Relationship tables** depend on the concept table
4. **Drug strength** depends on concept table

### Advantages of Manual Method

- **Direct PostgreSQL COPY** - fastest loading method
- **Dependency control** - load files in correct order
- **Error visibility** - immediate feedback on issues
- **No additional dependencies** - uses only PostgreSQL tools

## Method 2: Alternative Loading Methods

### Vocabulary Files Overview

After downloading and extracting vocabularies from Athena, you should have these files:

| File | Description | Rows (approx.) |
|------|-------------|----------------|
| `CONCEPT.csv` | All concepts across all vocabularies | ~3.28M |
| `VOCABULARY.csv` | Information about the vocabularies | ~49 |
| `DOMAIN.csv` | Domains for concepts | ~51 |
| `CONCEPT_CLASS.csv` | Classes for concepts | ~434 |
| `CONCEPT_RELATIONSHIP.csv` | Relationships between concepts | ~15.8M |
| `RELATIONSHIP.csv` | Types of relationships | ~723 |
| `CONCEPT_SYNONYM.csv` | Alternative names for concepts | ~2.6M |
| `CONCEPT_ANCESTOR.csv` | Hierarchical relationships | ~11.7M |
| `DRUG_STRENGTH.csv` | Drug strength information | ~205K |

### Interactive PostgreSQL Session

If you prefer an interactive approach:

```bash
# Connect to OMOP database
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm

# Then run COPY commands interactively:
\COPY vocabulary FROM 'VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
\COPY domain FROM 'DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
-- ... continue with other files
```

> **Note**: Ensure you're in the vocabulary directory (`data/vocabulary/omop_v5_20250630`) before running COPY commands.

### Method 3: Python Script (Advanced)

For automated loading with progress tracking, you can create a Python script:

```python
import os
import subprocess
import time

def load_vocabulary_table(table_name, csv_file):
    """Load a single vocabulary table using PostgreSQL COPY"""
    cmd = [
        'psql', '-U', 'omop', '-d', 'omop_cdm', '-c',
        f"\\COPY public.{table_name} FROM '{csv_file}' WITH DELIMITER E'\\t' CSV HEADER QUOTE E'\\b'"
    ]

    env = os.environ.copy()
    env['PGPASSWORD'] = 'omop_secure_2024'

    print(f"Loading {csv_file} into {table_name}...")
    start_time = time.time()

    result = subprocess.run(cmd, env=env, capture_output=True, text=True)

    if result.returncode == 0:
        elapsed = time.time() - start_time
        print(f"✅ Loaded {table_name} in {elapsed:.1f}s")
    else:
        print(f"❌ Error loading {table_name}: {result.stderr}")

# Load tables in dependency order
vocab_dir = "data/vocabulary/omop_v5_20250630"
os.chdir(vocab_dir)

tables = [
    ('vocabulary', 'VOCABULARY.csv'),
    ('domain', 'DOMAIN.csv'),
    ('concept_class', 'CONCEPT_CLASS.csv'),
    ('relationship', 'RELATIONSHIP.csv'),
    ('concept', 'CONCEPT.csv'),
    ('concept_relationship', 'CONCEPT_RELATIONSHIP.csv'),
    ('concept_ancestor', 'CONCEPT_ANCESTOR.csv'),
    ('concept_synonym', 'CONCEPT_SYNONYM.csv'),
    ('drug_strength', 'DRUG_STRENGTH.csv')
]

for table_name, csv_file in tables:
    load_vocabulary_table(table_name, csv_file)

print("🎉 Vocabulary loading completed!")
```

## Verification and Validation

After loading vocabularies, verify the data was loaded correctly.

### Quick Verification Commands

```bash
# 1. Check total concept count (should be ~3.28M)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_concepts FROM concept;"

# 2. Check vocabulary distribution
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, COUNT(*) as concept_count FROM concept GROUP BY vocabulary_id ORDER BY concept_count DESC LIMIT 10;"

# 3. Verify key vocabularies are present
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, vocabulary_name, vocabulary_version FROM vocabulary WHERE vocabulary_id IN ('SNOMED', 'LOINC', 'RxNorm', 'CPT4', 'ICD10CM');"

# 4. Check relationship counts
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_relationships FROM concept_relationship;"

# 5. Verify CPT4 reconstitution was successful
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as cpt4_concepts FROM concept WHERE vocabulary_id = 'CPT4';"
```

### Expected Results (v20250630)

After successful loading, you should see:

| Vocabulary | Expected Concepts | Purpose |
|------------|------------------|---------|
| **NDC** | ~1,254,857 | National Drug Code identifiers |
| **SNOMED** | ~1,089,088 | Clinical conditions, procedures, findings |
| **RxNorm** | ~311,332 | Normalized medication names |
| **LOINC** | ~274,904 | Laboratory tests, measurements, observations |
| **ICD10CM** | ~99,421 | Clinical modification diagnoses |
| **CPT4** | ~17,749 | Current Procedural Terminology (reconstituted) |
| **ICD10** | ~16,638 | International disease classification |
| **ATC** | ~7,223 | Anatomical Therapeutic Chemical classification |

**Total Statistics:**
- **Total concepts**: ~3,280,000
- **Total relationships**: ~15,800,000
- **Total ancestors**: ~11,700,000

### Detailed Verification

```sql
-- Count records in all vocabulary tables
SELECT 'concept' AS table_name, COUNT(*) AS record_count FROM concept
UNION ALL
SELECT 'vocabulary', COUNT(*) FROM vocabulary
UNION ALL
SELECT 'domain', COUNT(*) FROM domain
UNION ALL
SELECT 'concept_class', COUNT(*) FROM concept_class
UNION ALL
SELECT 'concept_relationship', COUNT(*) FROM concept_relationship
UNION ALL
SELECT 'relationship', COUNT(*) FROM relationship
UNION ALL
SELECT 'concept_synonym', COUNT(*) FROM concept_synonym
UNION ALL
SELECT 'concept_ancestor', COUNT(*) FROM concept_ancestor
UNION ALL
SELECT 'source_to_concept_map', COUNT(*) FROM source_to_concept_map
UNION ALL
SELECT 'drug_strength', COUNT(*) FROM drug_strength
ORDER BY table_name;
```

## Troubleshooting

### Common Issues

1. **Permission Issues**: Ensure the `omop` user has write permissions to all tables
   ```bash
   # Check permissions
   PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\dp concept"
   ```

2. **Disk Space**: Vocabulary files are large (~1.5GB). Check available space:
   ```bash
   df -h
   ```

3. **Memory Issues**: PostgreSQL may use significant memory during loading. Monitor with:
   ```bash
   top -p $(pgrep postgres)
   ```

4. **File Path Issues**: Ensure you're in the correct directory:
   ```bash
   pwd  # Should show: .../data/vocabulary/omop_v5_20250630
   ls *.csv  # Should list all vocabulary files
   ```

5. **Connection Issues**: Verify database connection:
   ```bash
   PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT current_database(), current_user;"
   ```

### Performance Tips

- **Load during off-peak hours** if sharing the database
- **Increase PostgreSQL memory settings** for faster loading
- **Monitor progress** with `\timing` in psql for timing information
- **Use SSD storage** for better I/O performance

## References

1. [OHDSI Vocabulary Loading Documentation](https://ohdsi.github.io/CommonDataModel/vocabulary.html)
2. [The Book of OHDSI: Chapter 4 - The Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html)
3. [PostgreSQL COPY Command Documentation](https://www.postgresql.org/docs/current/sql-copy.html)
4. [Pandas to_sql Documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.to_sql.html)
