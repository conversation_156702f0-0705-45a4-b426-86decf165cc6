# OMOP Vocabulary Loading Guide

This guide explains how to load OMOP vocabularies into your OMOP CDM database using both automated and manual methods.

## Prerequisites

Before loading vocabularies, ensure you have:

- ✅ **OMOP CDM database** set up (see [PostgreSQL Setup](../database/postgresql_setup.md))
- ✅ **Vocabulary files** downloaded from [Athena](athena_setup.md)
- ✅ **Environment variables** configured in `.env` file
- ✅ **Python environment** `fhir-omop` activated

## Quick Verification

Verify your setup before proceeding:

```bash
# Check database connection
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) FROM concept;"

# Should return: 0 (empty vocabulary tables)
```

## Method 1: Automated Loading (Recommended)

### Using the Python Script

We provide an optimized Python script that handles the entire loading process:

```bash
# Ensure vocabulary files are in the correct location
ls data/vocabulary/athena_download/

# Run the automated loader
python scripts/load_vocabularies.py
```

### Expected Output

The script provides detailed progress tracking:

```
🚀 OMOP Vocabulary Loader Starting...
📂 Vocabulary directory: data/vocabulary/athena_download
🔌 Connecting to database: omop_cdm
✅ Database connection established
📁 Loading VOCABULARY.csv → vocabulary (150 rows)
✅ Loaded 150 rows in 0.1s (1,500 rows/sec)
📁 Loading CONCEPT.csv → concept (6,234,567 rows)
✅ Loaded 6,234,567 rows in 45.2s (137,890 rows/sec)
...
🎉 Vocabulary loading completed!
📊 Total records loaded: 75,432,123
⏱️  Total time: 180.5 seconds
```

### Advantages of Automated Method

- **Optimized performance** using PostgreSQL COPY commands
- **Dependency management** loads files in correct order
- **Progress tracking** with detailed statistics
- **Error handling** with clear error messages
- **Verification** checks key tables after loading

## Method 2: Manual Loading

### Vocabulary Files

After downloading and extracting vocabularies from Athena, you should have the following files:

| File | Description |
|------|-------------|
| `CONCEPT.csv` | All concepts across all vocabularies |
| `VOCABULARY.csv` | Information about the vocabularies |
| `DOMAIN.csv` | Domains for concepts |
| `CONCEPT_CLASS.csv` | Classes for concepts |
| `CONCEPT_RELATIONSHIP.csv` | Relationships between concepts |
| `RELATIONSHIP.csv` | Types of relationships |
| `CONCEPT_SYNONYM.csv` | Alternative names for concepts |
| `CONCEPT_ANCESTOR.csv` | Hierarchical relationships |
| `SOURCE_TO_CONCEPT_MAP.csv` | Mappings from source codes to standard concepts |
| `DRUG_STRENGTH.csv` | Drug strength information |

### Manual PostgreSQL Loading (macOS)

If you prefer manual control over the loading process:

#### Step 1: Connect to Database
```bash
# Connect to OMOP database
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm
```

#### Step 2: Load Files in Dependency Order

**Critical**: Load files in this exact order to respect foreign key dependencies:

```sql
-- 1. Load vocabulary metadata first
\COPY vocabulary FROM '/absolute/path/to/data/vocabulary/athena_download/VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';

-- 2. Load domains and concept classes
\COPY domain FROM '/absolute/path/to/data/vocabulary/athena_download/DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
\COPY concept_class FROM '/absolute/path/to/data/vocabulary/athena_download/CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';

-- 3. Load main concepts table (largest file - may take 5-10 minutes)
\COPY concept FROM '/absolute/path/to/data/vocabulary/athena_download/CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';

-- 4. Load relationship metadata
\COPY relationship FROM '/absolute/path/to/data/vocabulary/athena_download/RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';

-- 5. Load concept relationships (second largest file)
\COPY concept_relationship FROM '/absolute/path/to/data/vocabulary/athena_download/CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';

-- 6. Load remaining tables
\COPY concept_synonym FROM '/absolute/path/to/data/vocabulary/athena_download/CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
\COPY concept_ancestor FROM '/absolute/path/to/data/vocabulary/athena_download/CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
\COPY drug_strength FROM '/absolute/path/to/data/vocabulary/athena_download/DRUG_STRENGTH.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
```

#### Important Notes for Manual Loading

- **Use absolute paths**: Relative paths may not work in psql
- **Loading time**: CONCEPT.csv can take 5-10 minutes for large vocabularies
- **Memory usage**: PostgreSQL may use significant memory during loading
- **Error handling**: If one file fails, you may need to truncate tables and restart

### Option 2: Using Python Script

1. **Create a script** named `load_vocabularies.py`:
   ```python
   import os
   import pandas as pd
   from sqlalchemy import create_engine
   import time

   # Database connection
   engine = create_engine('postgresql://username:password@localhost:5432/omop_cdm')

   # Vocabulary directory
   vocab_dir = '/path/to/vocabulary'

   # Vocabulary tables and their corresponding files
   vocab_tables = {
       'concept': 'CONCEPT.csv',
       'vocabulary': 'VOCABULARY.csv',
       'domain': 'DOMAIN.csv',
       'concept_class': 'CONCEPT_CLASS.csv',
       'concept_relationship': 'CONCEPT_RELATIONSHIP.csv',
       'relationship': 'RELATIONSHIP.csv',
       'concept_synonym': 'CONCEPT_SYNONYM.csv',
       'concept_ancestor': 'CONCEPT_ANCESTOR.csv',
       'source_to_concept_map': 'SOURCE_TO_CONCEPT_MAP.csv',
       'drug_strength': 'DRUG_STRENGTH.csv'
   }

   # Load each vocabulary file
   for table, file_name in vocab_tables.items():
       file_path = os.path.join(vocab_dir, file_name)
       if not os.path.exists(file_path):
           print(f"File not found: {file_path}")
           continue
       
       print(f"Loading {file_name} into {table}...")
       start_time = time.time()
       
       # Read CSV in chunks to avoid memory issues
       chunksize = 100000
       for i, chunk in enumerate(pd.read_csv(file_path, sep='\t', chunksize=chunksize)):
           chunk.to_sql(table, engine, if_exists='append' if i > 0 else 'replace', index=False)
           print(f"  Loaded chunk {i+1} ({chunksize} rows)")
       
       elapsed_time = time.time() - start_time
       print(f"Completed loading {file_name} in {elapsed_time:.2f} seconds")

   print("Vocabulary loading completed")
   ```

2. **Run the script**:
   ```bash
   python load_vocabularies.py
   ```

## Loading Vocabularies into SQLite

### Using Python Script

1. **Create a script** named `load_vocabularies_sqlite.py`:
   ```python
   import os
   import pandas as pd
   import sqlite3
   import time

   # Database connection
   conn = sqlite3.connect('/path/to/omop_cdm.db')

   # Vocabulary directory
   vocab_dir = '/path/to/vocabulary'

   # Vocabulary tables and their corresponding files
   vocab_tables = {
       'concept': 'CONCEPT.csv',
       'vocabulary': 'VOCABULARY.csv',
       'domain': 'DOMAIN.csv',
       'concept_class': 'CONCEPT_CLASS.csv',
       'concept_relationship': 'CONCEPT_RELATIONSHIP.csv',
       'relationship': 'RELATIONSHIP.csv',
       'concept_synonym': 'CONCEPT_SYNONYM.csv',
       'concept_ancestor': 'CONCEPT_ANCESTOR.csv',
       'source_to_concept_map': 'SOURCE_TO_CONCEPT_MAP.csv',
       'drug_strength': 'DRUG_STRENGTH.csv'
   }

   # Load each vocabulary file
   for table, file_name in vocab_tables.items():
       file_path = os.path.join(vocab_dir, file_name)
       if not os.path.exists(file_path):
           print(f"File not found: {file_path}")
           continue
       
       print(f"Loading {file_name} into {table}...")
       start_time = time.time()
       
       # Read CSV in chunks to avoid memory issues
       chunksize = 100000
       for i, chunk in enumerate(pd.read_csv(file_path, sep='\t', chunksize=chunksize)):
           chunk.to_sql(table, conn, if_exists='append' if i > 0 else 'replace', index=False)
           print(f"  Loaded chunk {i+1} ({chunksize} rows)")
       
       elapsed_time = time.time() - start_time
       print(f"Completed loading {file_name} in {elapsed_time:.2f} seconds")

   # Close connection
   conn.close()

   print("Vocabulary loading completed")
   ```

2. **Run the script**:
   ```bash
   python load_vocabularies_sqlite.py
   ```

## Using the Vocabulary Manager

The OMOP module includes a `VocabularyManager` class that simplifies vocabulary loading:

```python
from fhir_omop.omop.db.vocabulary_manager import VocabularyManager
from fhir_omop.omop.db.db_manager import OmopDBManager

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Initialize vocabulary manager
vocab_manager = VocabularyManager(db_manager)

# Load vocabularies
vocab_manager.load_vocabulary('/path/to/vocabulary')

# Check vocabulary loading
vocab_counts = vocab_manager.check_vocabulary()
for table, count in vocab_counts.items():
    print(f"{table}: {count} records")
```

## Verification and Validation

After loading vocabularies, it's critical to verify the data was loaded correctly.

### Quick Verification Commands

```bash
# 1. Check total concept count (should be 6M+ for full vocabularies)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_concepts FROM concept;"

# 2. Check vocabulary distribution
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, COUNT(*) as concept_count FROM concept GROUP BY vocabulary_id ORDER BY COUNT(*) DESC LIMIT 10;"

# 3. Verify key vocabularies are present
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, vocabulary_name FROM vocabulary WHERE vocabulary_id IN ('SNOMED', 'LOINC', 'RxNorm', 'Gender', 'Race');"
```

### Expected Results

After successful loading, you should see:

| Vocabulary | Expected Concepts | Purpose |
|------------|------------------|---------|
| **SNOMED** | ~400,000 | Clinical conditions, procedures |
| **LOINC** | ~100,000 | Laboratory tests, measurements |
| **RxNorm** | ~200,000 | Medications and drugs |
| **Gender** | ~10 | Patient gender values |
| **Race** | ~50 | Patient race/ethnicity values |

### Detailed Verification

```sql
-- Count records in all vocabulary tables
SELECT 'concept' AS table_name, COUNT(*) AS record_count FROM concept
UNION ALL
SELECT 'vocabulary', COUNT(*) FROM vocabulary
UNION ALL
SELECT 'domain', COUNT(*) FROM domain
UNION ALL
SELECT 'concept_class', COUNT(*) FROM concept_class
UNION ALL
SELECT 'concept_relationship', COUNT(*) FROM concept_relationship
UNION ALL
SELECT 'relationship', COUNT(*) FROM relationship
UNION ALL
SELECT 'concept_synonym', COUNT(*) FROM concept_synonym
UNION ALL
SELECT 'concept_ancestor', COUNT(*) FROM concept_ancestor
UNION ALL
SELECT 'source_to_concept_map', COUNT(*) FROM source_to_concept_map
UNION ALL
SELECT 'drug_strength', COUNT(*) FROM drug_strength
ORDER BY table_name;
```

### SQLite

```python
import sqlite3

conn = sqlite3.connect('/path/to/omop_cdm.db')
cursor = conn.cursor()

tables = [
    'concept', 'vocabulary', 'domain', 'concept_class',
    'concept_relationship', 'relationship', 'concept_synonym',
    'concept_ancestor', 'source_to_concept_map', 'drug_strength'
]

for table in tables:
    cursor.execute(f"SELECT COUNT(*) FROM {table}")
    count = cursor.fetchone()[0]
    print(f"{table}: {count} records")

conn.close()
```

## Troubleshooting

### Common Issues

1. **Memory Errors**: When loading large files, you may encounter memory errors. Use chunking as shown in the examples.

2. **Permission Issues**: Ensure the database user has permission to write to the tables.

3. **Disk Space**: Vocabulary files can be large. Ensure you have enough disk space.

4. **Timeout Issues**: Loading large files may take time. Increase timeout settings if necessary.

5. **Encoding Issues**: If you encounter encoding errors, check the file encoding and adjust the loading process accordingly.

## References

1. [OHDSI Vocabulary Loading Documentation](https://ohdsi.github.io/CommonDataModel/vocabulary.html)
2. [The Book of OHDSI: Chapter 4 - The Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html)
3. [PostgreSQL COPY Command Documentation](https://www.postgresql.org/docs/current/sql-copy.html)
4. [Pandas to_sql Documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.to_sql.html)
